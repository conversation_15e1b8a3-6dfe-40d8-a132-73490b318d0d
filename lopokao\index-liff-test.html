<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>融氏古早味手工蘿蔔糕 - 傳統美味，用心製作</title>
    <meta name="description" content="融氏古早味手工蘿蔔糕，堅持純手工製作，使用在地新鮮食材，提供古早味蘿蔔糕、芋頭糕、台式鹹蘿蔔糕等多種美味選擇。">
    
    <!-- LIFF SDK -->
    <script charset="utf-8" src="https://static.line-scdn.net/liff/edge/2/sdk.js"></script>
    
    <!-- 使用更穩定的 CDN -->
    <script crossorigin src="https://unpkg.com/react@18/umd/react.production.min.js"></script>
    <script crossorigin src="https://unpkg.com/react-dom@18/umd/react-dom.production.min.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    
    <!-- 使用內嵌 TailwindCSS 而非 CDN -->
    <style>
        /* 內嵌關鍵 TailwindCSS 樣式 */
        *,::before,::after{box-sizing:border-box;border-width:0;border-style:solid;border-color:#e5e7eb}
        html{line-height:1.5;font-family:ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",Arial,"Noto Sans",sans-serif}
        body{margin:0;line-height:inherit}
        
        .container{width:100%;margin:0 auto;padding:0 1rem}
        @media (min-width:640px){.container{max-width:640px}}
        @media (min-width:768px){.container{max-width:768px}}
        @media (min-width:1024px){.container{max-width:1024px}}
        @media (min-width:1280px){.container{max-width:1280px}}
        
        .flex{display:flex}
        .items-center{align-items:center}
        .justify-center{justify-content:center}
        .justify-between{justify-content:space-between}
        .text-center{text-align:center}
        .bg-red-500{background-color:#ef4444}
        .bg-white{background-color:#fff}
        .text-white{color:#fff}
        .text-gray-600{color:#4b5563}
        .text-gray-800{color:#1f2937}
        .p-4{padding:1rem}
        .p-6{padding:1.5rem}
        .py-2{padding-top:0.5rem;padding-bottom:0.5rem}
        .px-4{padding-left:1rem;padding-right:1rem}
        .mb-4{margin-bottom:1rem}
        .mb-6{margin-bottom:1.5rem}
        .mt-8{margin-top:2rem}
        .rounded{border-radius:0.25rem}
        .rounded-lg{border-radius:0.5rem}
        .shadow{box-shadow:0 1px 3px 0 rgb(0 0 0 / 0.1)}
        .w-full{width:100%}
        .h-12{height:3rem}
        .text-lg{font-size:1.125rem;line-height:1.75rem}
        .text-xl{font-size:1.25rem;line-height:1.75rem}
        .text-2xl{font-size:1.5rem;line-height:2rem}
        .text-3xl{font-size:1.875rem;line-height:2.25rem}
        .font-bold{font-weight:700}
        .font-semibold{font-weight:600}
        .transition{transition:all 0.15s ease}
        .hover\:bg-red-600:hover{background-color:#dc2626}
        
        /* LIFF 專用樣式 */
        body.liff-mode {
            padding-top: 0;
            margin: 0;
            background-color: #f8fafc;
        }
        
        .liff-header {
            background: linear-gradient(135deg, #ef4444, #dc2626);
            color: white;
            padding: 1rem;
            text-align: center;
            position: sticky;
            top: 0;
            z-index: 1000;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .liff-notice {
            background: #fef3c7;
            border: 1px solid #f59e0b;
            color: #92400e;
            padding: 0.75rem;
            margin: 1rem;
            border-radius: 0.5rem;
            font-size: 0.875rem;
        }
    </style>
    
    <!-- 載入自訂樣式 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <link href="styles/main.css" rel="stylesheet">
    <link href="styles/header.css" rel="stylesheet">
    <link href="styles/hero.css" rel="stylesheet">
    <link href="styles/brand-story.css" rel="stylesheet">
    <link href="styles/products.css" rel="stylesheet">
    <link href="styles/process.css" rel="stylesheet">
    <link href="styles/reviews.css" rel="stylesheet">
    <link href="styles/order.css" rel="stylesheet">
    <link href="styles/footer.css" rel="stylesheet">
    <link href="styles/admin.css" rel="stylesheet">
    <link href="styles/floating-buttons.css" rel="stylesheet">
    <link href="styles/delivery-notice-modal.css" rel="stylesheet">
    
    <link rel="icon" href="https://app.trickle.so/storage/app/LOGO-2.webp">
</head>

<body>
    <!-- LIFF 環境提示 -->
    <div id="liff-notice" class="liff-header" style="display: none;">
        <h2 class="text-xl font-bold">融氏古早味手工蘿蔔糕</h2>
        <p class="text-sm">LINE 專屬訂購頁面</p>
    </div>
    
    <!-- LIFF 使用提示 -->
    <div id="liff-info" class="liff-notice" style="display: none;">
        📱 您正在 LINE 中瀏覽，訂單完成後將自動返回 LINE 聊天室
    </div>
    
    <div id="root"></div>
    
    <!-- LIFF 初始化腳本 -->
    <script>
        let isLiffEnvironment = false;
        let liffProfile = null;
        
        // LIFF 初始化函數
        async function initializeLiffIfNeeded() {
            if (typeof liff !== 'undefined') {
                try {
                    await liff.init({
                        liffId: '2007619149-RYmOkjxJ' // 請替換為您的實際 LIFF ID
                    });
                    
                    isLiffEnvironment = true;
                    document.body.classList.add('liff-mode');
                    document.getElementById('liff-notice').style.display = 'block';
                    document.getElementById('liff-info').style.display = 'block';
                    
                    // 獲取用戶資料
                    if (liff.isLoggedIn()) {
                        liffProfile = await liff.getProfile();
                        console.log('LIFF 用戶資料:', liffProfile);
                        
                        // 將用戶資料存到全域變數供 React 組件使用
                        window.liffUserProfile = liffProfile;
                    }
                    
                    console.log('LIFF 環境已初始化');
                } catch (error) {
                    console.error('LIFF 初始化失敗:', error);
                    // 即使失敗也繼續載入頁面
                }
            }
            
            // 設定全域 LIFF 狀態
            window.isLiffEnvironment = isLiffEnvironment;
        }
        
        // 在載入其他腳本前先初始化 LIFF
        initializeLiffIfNeeded();
    </script>
    
    <!-- 載入應用程式腳本 -->
    <script src="utils/errorUtils.js"></script>
    <script src="utils/stagewise.js"></script>
    <script type="text/babel" src="utils/cityDistricts.js"></script>
    <script type="text/babel" src="components/Header.js"></script>
    <script type="text/babel" src="components/ProcessSection.js"></script>
    <script type="text/babel" src="components/Hero.js"></script>
    <script type="text/babel" src="components/BrandStory.js"></script>
    <script type="text/babel" src="components/Products.js"></script>
    <script type="text/babel" src="components/ProductCard.js"></script>
    <script type="text/babel" src="components/CustomerReviews.js"></script>
    <script type="text/babel" src="components/MediaSection.js"></script>
    <script type="text/babel" src="components/Storage.js"></script>
    <script type="text/babel" src="components/OrderInstructions.js"></script>
    <script type="text/babel" src="components/OrderGuide.js"></script>
    <script type="text/babel" src="components/OrderForm.js?v=1.2"></script>
    <script type="text/babel" src="components/Footer.js?v=1.4"></script>
    <script type="text/babel" src="components/FloatingButtons.js"></script>
    <script type="text/babel" src="components/DeliveryNoticeModal.js"></script>
    <script type="text/babel" src="components/admin/Login.js"></script>
    <script type="text/babel" src="components/admin/AdminDashboard.js"></script>
    <script type="text/babel" src="utils/orderUtils.js"></script>
    <script type="text/babel" src="utils/sheetsUtils.js?v=1.1"></script>
    <script type="text/babel" src="utils/adminUtils.js"></script>
    <script type="text/babel" src="app.js"></script>
</body>
</html>