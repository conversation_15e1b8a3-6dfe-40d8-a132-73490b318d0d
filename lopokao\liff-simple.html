<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>融氏古早味蘿蔔糕 - LINE 訂購</title>
    
    <!-- LINE LIFF SDK -->
    <script charset="utf-8" src="https://static.line-scdn.net/liff/edge/2/sdk.js"></script>
    
    <!-- 內嵌 TailwindCSS 核心樣式 -->
    <style>
        /* 內嵌必要的 Tailwind 樣式 */
        .container { max-width: 414px; margin: 0 auto; }
        .bg-red-500 { background-color: #ef4444; }
        .text-white { color: white; }
        .p-4 { padding: 1rem; }
        .mb-4 { margin-bottom: 1rem; }
        .rounded { border-radius: 0.25rem; }
        .flex { display: flex; }
        .items-center { align-items: center; }
        .justify-center { justify-content: center; }
        .w-full { width: 100%; }
        .h-12 { height: 3rem; }
        
        /* LIFF 專用樣式 */
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background-color: #f8fafc;
        }
        
        .liff-container {
            max-width: 414px;
            min-height: 100vh;
            margin: 0 auto;
            background: white;
        }
    </style>
</head>
<body>
    <div id="liff-app" class="liff-container">
        <div class="p-4">
            <h1 class="text-xl font-bold mb-4">載入中...</h1>
        </div>
    </div>
    
    <!-- 使用原生 JavaScript 而非 React -->
    <script>
        let liffReady = false;
        
        // 初始化 LIFF
        async function initializeLIFF() {
            try {
                if (typeof liff === 'undefined') {
                    console.warn('非 LIFF 環境，使用測試模式');
                    renderApp();
                    return;
                }
                
                await liff.init({
                    liffId: '2007619149-RYmOkjxJ'
                });
                
                if (!liff.isLoggedIn()) {
                    liff.login();
                    return;
                }
                
                const profile = await liff.getProfile();
                console.log('用戶資料:', profile);
                
                liffReady = true;
                renderApp(profile);
                
            } catch (error) {
                console.error('LIFF 初始化失敗:', error);
                renderApp();
            }
        }
        
        // 渲染應用程式
        function renderApp(userProfile = null) {
            const app = document.getElementById('liff-app');
            app.innerHTML = `
                <div class="p-4">
                    <h1 class="text-xl font-bold mb-4">融氏古早味蘿蔔糕</h1>
                    ${userProfile ? `<p class="mb-4">歡迎，${userProfile.displayName}！</p>` : ''}
                    <button onclick="redirectToMainSite()" class="w-full h-12 bg-red-500 text-white rounded">
                        前往完整訂購頁面
                    </button>
                </div>
            `;
        }
        
        // 重導向到主網站
        function redirectToMainSite() {
            if (liffReady && liff.isInClient()) {
                liff.openWindow({
                    url: window.location.origin + '/index.html#order',
                    external: false
                });
            } else {
                window.open('/index.html#order', '_blank');
            }
        }
        
        // 啟動應用程式
        document.addEventListener('DOMContentLoaded', initializeLIFF);
    </script>
</body>
</html>