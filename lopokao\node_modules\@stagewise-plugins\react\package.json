{"name": "@stagewise-plugins/react", "version": "0.6.2", "type": "module", "keywords": ["stagewise", "toolbar", "ai", "devtool", "agent", "interaction"], "author": "tiq UG (haftungsbeschränkt)", "homepage": "https://stagewise.io", "bugs": {"url": "https://github.com/stagewise-io/stagewise/issues"}, "repository": {"type": "git", "url": "https://github.com/stagewise-io/stagewise.git", "directory": "packages/toolbar"}, "publishConfig": {"access": "public"}, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.es.js", "require": "./dist/index.cjs.js", "default": "./dist/index.es.js"}}, "main": "dist/index.cjs.js", "module": "dist/index.es.js", "types": "dist/index.d.ts", "files": ["dist"], "license": "AGPL-3.0-only", "dependencies": {}, "devDependencies": {"typescript": "~5.8.3", "react": "^19.1.0", "react-dom": "^19.1.0", "@types/react": "^19.1.8", "tsx": "^4.20.3", "@stagewise/toolbar": "0.6.2", "@stagewise/plugin-builder": "0.6.2"}, "peerDependencies": {"@stagewise/toolbar": "0.6.2"}, "scripts": {"clean": "rm -rf .turbo node_modules", "dev": "tsc -b && tsx build.config.ts development", "build": "tsc -b && tsx build.config.ts production"}}