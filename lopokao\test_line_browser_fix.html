<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LINE 瀏覽器地址選擇修正測試</title>
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {
            font-family: 'Microsoft JhengHei', sans-serif;
        }
    </style>
</head>
<body class="bg-gray-100 p-4">
    <div class="max-w-2xl mx-auto">
        <div class="bg-white p-6 rounded-lg shadow-lg">
            <h1 class="text-2xl font-bold mb-4 text-center">LINE 瀏覽器地址選擇修正測試</h1>
            
            <div class="mb-4 p-4 bg-blue-50 rounded-lg">
                <h2 class="font-bold text-blue-800 mb-2">測試說明</h2>
                <ul class="text-sm text-blue-700 space-y-1">
                    <li>• 此頁面用於測試 LINE 瀏覽器中地址選擇的修正</li>
                    <li>• 在 LINE 瀏覽器中，縣市選擇應顯示「請選擇縣市」而非直接顯示「臺北市」</li>
                    <li>• 修正包含：瀏覽器檢測、強制重新渲染、特殊提示訊息</li>
                </ul>
            </div>

            <div id="browser-info" class="mb-4 p-3 bg-gray-50 rounded">
                <h3 class="font-bold mb-2">瀏覽器資訊</h3>
                <p id="user-agent" class="text-sm text-gray-600"></p>
                <p id="is-line" class="text-sm font-medium"></p>
            </div>

            <div id="order-form-container">
                <!-- OrderForm 組件將在這裡渲染 -->
            </div>
        </div>
    </div>

    <!-- 載入必要的腳本 -->
    <script src="./dist/cityDistricts.js"></script>
    <script src="./utils/orderUtils.js"></script>
    <script src="./utils/sheetsUtils.js"></script>
    <script src="./utils/errorUtils.js"></script>
    <script src="./dist/OrderForm.js"></script>

    <script>
        // 顯示瀏覽器資訊
        document.getElementById('user-agent').textContent = 'User Agent: ' + navigator.userAgent;
        
        const isLineApp = navigator.userAgent.includes('Line') || navigator.userAgent.includes('LINE');
        const isLineElement = document.getElementById('is-line');
        
        if (isLineApp) {
            isLineElement.textContent = '✅ 檢測到 LINE 瀏覽器';
            isLineElement.className = 'text-sm font-medium text-green-600';
        } else {
            isLineElement.textContent = '❌ 非 LINE 瀏覽器';
            isLineElement.className = 'text-sm font-medium text-red-600';
        }

        // 渲染 OrderForm 組件
        const container = document.getElementById('order-form-container');
        const root = ReactDOM.createRoot(container);
        
        // 確保 cityDistricts 已載入
        if (typeof cityDistricts === 'undefined') {
            console.error('cityDistricts 未載入');
        } else {
            console.log('cityDistricts 已載入，包含', Object.keys(cityDistricts).length, '個縣市');
        }

        // 渲染組件
        try {
            root.render(React.createElement(OrderForm));
            console.log('OrderForm 組件已成功渲染');
        } catch (error) {
            console.error('OrderForm 渲染失敗:', error);
            container.innerHTML = '<div class="text-red-600 p-4">組件載入失敗: ' + error.message + '</div>';
        }
    </script>
</body>
</html>
