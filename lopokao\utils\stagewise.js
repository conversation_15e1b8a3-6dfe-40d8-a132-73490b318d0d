// Stagewise 工具列初始化
// 僅在開發模式下載入

// 檢查是否為開發模式
const isDevelopment = window.location.hostname === 'localhost' || 
                     window.location.hostname === '127.0.0.1' ||
                     window.location.hostname.includes('dev') ||
                     window.location.hostname.includes('test');

if (isDevelopment) {
    // 動態載入 stagewise 套件
    const loadStagewise = async () => {
        try {
            // 使用動態 import 載入 stagewise
            const { initToolbar } = await import('@stagewise/toolbar');
            const ReactPlugin = await import('@stagewise-plugins/react');
            
            // 初始化工具列
            initToolbar({
                plugins: [ReactPlugin.default],
            });
            
            console.log('Stagewise 工具列已載入');
        } catch (error) {
            console.warn('Stagewise 工具列載入失敗:', error);
        }
    };

    // 在頁面載入完成後初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', loadStagewise);
    } else {
        loadStagewise();
    }
} 