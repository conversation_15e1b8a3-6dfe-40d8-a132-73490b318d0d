# LINE 瀏覽器地址選擇修正方案

## 問題描述

在 LINE 瀏覽器中，訂購表單的「宅配到府地址選擇」功能出現異常：
- **正常行為**：縣市下拉選單應顯示「請選擇縣市」
- **異常行為**：在 LINE 瀏覽器中直接顯示「臺北市」

## 問題原因

1. **LINE 瀏覽器特殊行為**：LINE 內建瀏覽器可能忽略 `value=""` 的 option，自動選擇第一個有值的選項
2. **DOM 渲染時機**：React 狀態更新時，LINE 瀏覽器的 DOM 渲染順序與標準瀏覽器不同
3. **選項順序問題**：在 `cityDistricts.js` 中，"臺北市"是第一個縣市

## 修正方案

### 1. 瀏覽器檢測
```javascript
// 檢測是否為 LINE 瀏覽器
const [isLineApp, setIsLineApp] = React.useState(false);

React.useEffect(() => {
    const userAgent = navigator.userAgent || '';
    const isLine = userAgent.includes('Line') || userAgent.includes('LINE');
    setIsLineApp(isLine);
    if (isLine) {
        console.log('🔍 檢測到 LINE 瀏覽器，將使用特殊處理邏輯');
    }
}, []);
```

### 2. 強制重新渲染
```javascript
<select
    key={`city-select-${isLineApp}`} // 強制重新渲染
    value={formData.district}
    onChange={handleChange}
>
    <option value="" disabled={isLineApp} selected={!formData.district}>
        請選擇縣市
    </option>
    {/* 其他選項 */}
</select>
```

### 3. LINE 瀏覽器特殊處理
```javascript
// LINE 瀏覽器特殊處理：強制重置地址選擇
React.useEffect(() => {
    if (isLineApp && formData.district && formData.deliveryMethod !== '宅配到府') {
        console.log('🔧 LINE 瀏覽器：重置地址選擇');
        setFormData(prev => ({
            ...prev,
            district: '',
            area: ''
        }));
    }
}, [isLineApp, formData.deliveryMethod]);
```

### 4. 用戶提示
```javascript
{isLineApp && (
    <p className="text-xs text-blue-600 mt-1">
        💡 LINE 瀏覽器用戶：請點擊下拉選單選擇縣市
    </p>
)}
```

## 修正檔案

### 主要修改檔案
- `components/OrderForm.js` - 原始組件檔案
- `dist/OrderForm.js` - 編譯後的檔案

### 測試檔案
- `test_line_browser_fix.html` - 修正效果測試頁面

## 測試方法

1. **在 LINE 中測試**：
   - 開啟 LINE 應用程式
   - 瀏覽至測試頁面：`http://localhost/lopokao/test_line_browser_fix.html`
   - 檢查縣市選擇是否正常顯示「請選擇縣市」

2. **在一般瀏覽器中測試**：
   - 確保修正不影響正常瀏覽器的功能
   - 驗證地址選擇功能正常運作

## 技術細節

### 修正原理
1. **瀏覽器檢測**：透過 User Agent 識別 LINE 瀏覽器
2. **DOM 強制更新**：使用 key 屬性強制 React 重新渲染 select 元素
3. **選項屬性調整**：為 LINE 瀏覽器設定 disabled 和 selected 屬性
4. **狀態管理**：針對 LINE 瀏覽器的特殊狀態處理邏輯

### 兼容性保證
- 不影響其他瀏覽器的正常功能
- 保持原有的用戶體驗
- 向後兼容現有程式碼

## 部署說明

修正已直接應用到 `dist/OrderForm.js`，無需額外部署步驟。
建議在 LINE 環境中進行完整測試以確保修正效果。
