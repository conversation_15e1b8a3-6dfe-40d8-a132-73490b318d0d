/* TailwindCSS 完整編譯版本 */
/*! tailwindcss v3.3.0 | MIT License | https://tailwindcss.com */

/* Base styles */
*,::before,::after{box-sizing:border-box;border-width:0;border-style:solid;border-color:#e5e7eb}
::before,::after{--tw-content:''}
html{line-height:1.5;-webkit-text-size-adjust:100%;-moz-tab-size:4;tab-size:4;font-family:ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",<PERSON><PERSON>,"Noto Sans",sans-serif,"Apple Color Emoji","Segoe UI Emoji","Segoe UI Symbol","Noto Color Emoji";font-feature-settings:normal;font-variation-settings:normal}
body{margin:0;line-height:inherit}

/* Container */
.container{width:100%;margin-right:auto;margin-left:auto;padding-right:2rem;padding-left:2rem}
@media (min-width:640px){.container{max-width:640px}}
@media (min-width:768px){.container{max-width:768px}}
@media (min-width:1024px){.container{max-width:1024px}}
@media (min-width:1280px){.container{max-width:1280px}}
@media (min-width:1536px){.container{max-width:1536px}}

/* Layout */
.block{display:block}
.inline-block{display:inline-block}
.inline{display:inline}
.flex{display:flex}
.inline-flex{display:inline-flex}
.grid{display:grid}
.hidden{display:none}

/* Flexbox */
.flex-col{flex-direction:column}
.flex-wrap{flex-wrap:wrap}
.items-start{align-items:flex-start}
.items-end{align-items:flex-end}
.items-center{align-items:center}
.items-baseline{align-items:baseline}
.items-stretch{align-items:stretch}
.justify-start{justify-content:flex-start}
.justify-end{justify-content:flex-end}
.justify-center{justify-content:center}
.justify-between{justify-content:space-between}
.justify-around{justify-content:space-around}
.justify-evenly{justify-content:space-evenly}

/* Spacing */
.p-0{padding:0px}
.p-1{padding:0.25rem}
.p-2{padding:0.5rem}
.p-3{padding:0.75rem}
.p-4{padding:1rem}
.p-5{padding:1.25rem}
.p-6{padding:1.5rem}
.p-8{padding:2rem}
.p-10{padding:2.5rem}
.p-12{padding:3rem}

.px-2{padding-left:0.5rem;padding-right:0.5rem}
.px-3{padding-left:0.75rem;padding-right:0.75rem}
.px-4{padding-left:1rem;padding-right:1rem}
.px-6{padding-left:1.5rem;padding-right:1.5rem}
.px-8{padding-left:2rem;padding-right:2rem}

.py-2{padding-top:0.5rem;padding-bottom:0.5rem}
.py-3{padding-top:0.75rem;padding-bottom:0.75rem}
.py-4{padding-top:1rem;padding-bottom:1rem}
.py-6{padding-top:1.5rem;padding-bottom:1.5rem}
.py-8{padding-top:2rem;padding-bottom:2rem}

.m-0{margin:0px}
.m-1{margin:0.25rem}
.m-2{margin:0.5rem}
.m-3{margin:0.75rem}
.m-4{margin:1rem}
.m-auto{margin:auto}

.mx-auto{margin-left:auto;margin-right:auto}
.my-4{margin-top:1rem;margin-bottom:1rem}
.my-6{margin-top:1.5rem;margin-bottom:1.5rem}
.my-8{margin-top:2rem;margin-bottom:2rem}

.mb-2{margin-bottom:0.5rem}
.mb-3{margin-bottom:0.75rem}
.mb-4{margin-bottom:1rem}
.mb-6{margin-bottom:1.5rem}
.mb-8{margin-bottom:2rem}

.mt-4{margin-top:1rem}
.mt-6{margin-top:1.5rem}
.mt-8{margin-top:2rem}

/* Sizing */
.w-full{width:100%}
.w-auto{width:auto}
.w-1\/2{width:50%}
.w-1\/3{width:33.333333%}
.w-2\/3{width:66.666667%}
.w-1\/4{width:25%}
.w-3\/4{width:75%}

.h-auto{height:auto}
.h-full{height:100%}
.h-screen{height:100vh}
.h-12{height:3rem}
.h-16{height:4rem}
.h-20{height:5rem}

.max-w-xs{max-width:20rem}
.max-w-sm{max-width:24rem}
.max-w-md{max-width:28rem}
.max-w-lg{max-width:32rem}
.max-w-xl{max-width:36rem}
.max-w-2xl{max-width:42rem}
.max-w-4xl{max-width:56rem}
.max-w-6xl{max-width:72rem}
.max-w-7xl{max-width:80rem}

/* Typography */
.text-xs{font-size:0.75rem;line-height:1rem}
.text-sm{font-size:0.875rem;line-height:1.25rem}
.text-base{font-size:1rem;line-height:1.5rem}
.text-lg{font-size:1.125rem;line-height:1.75rem}
.text-xl{font-size:1.25rem;line-height:1.75rem}
.text-2xl{font-size:1.5rem;line-height:2rem}
.text-3xl{font-size:1.875rem;line-height:2.25rem}
.text-4xl{font-size:2.25rem;line-height:2.5rem}

.font-normal{font-weight:400}
.font-medium{font-weight:500}
.font-semibold{font-weight:600}
.font-bold{font-weight:700}

.text-left{text-align:left}
.text-center{text-align:center}
.text-right{text-align:right}

/* Colors */
.text-white{color:#fff}
.text-black{color:#000}
.text-gray-500{color:#6b7280}
.text-gray-600{color:#4b5563}
.text-gray-700{color:#374151}
.text-gray-800{color:#1f2937}
.text-gray-900{color:#111827}

.text-red-500{color:#ef4444}
.text-red-600{color:#dc2626}

.bg-white{background-color:#fff}
.bg-gray-50{background-color:#f9fafb}
.bg-gray-100{background-color:#f3f4f6}
.bg-gray-200{background-color:#e5e7eb}

.bg-red-500{background-color:#ef4444}
.bg-red-600{background-color:#dc2626}

/* Borders */
.border{border-width:1px}
.border-2{border-width:2px}
.border-gray-200{border-color:#e5e7eb}
.border-gray-300{border-color:#d1d5db}

.rounded{border-radius:0.25rem}
.rounded-md{border-radius:0.375rem}
.rounded-lg{border-radius:0.5rem}
.rounded-xl{border-radius:0.75rem}
.rounded-full{border-radius:9999px}

/* Effects */
.shadow{box-shadow:0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1)}
.shadow-md{box-shadow:0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)}
.shadow-lg{box-shadow:0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)}

/* Transitions */
.transition{transition-property:color,background-color,border-color,text-decoration-color,fill,stroke,opacity,box-shadow,transform,filter,backdrop-filter;transition-timing-function:cubic-bezier(0.4,0,0.2,1);transition-duration:150ms}

/* Hover states */
.hover\:bg-red-600:hover{background-color:#dc2626}
.hover\:text-red-600:hover{color:#dc2626}

/* Responsive utilities */
@media (min-width:640px){
  .sm\:text-lg{font-size:1.125rem;line-height:1.75rem}
  .sm\:text-xl{font-size:1.25rem;line-height:1.75rem}
  .sm\:text-2xl{font-size:1.5rem;line-height:2rem}
  .sm\:text-3xl{font-size:1.875rem;line-height:2.25rem}
}

@media (min-width:768px){
  .md\:flex{display:flex}
  .md\:hidden{display:none}
  .md\:text-xl{font-size:1.25rem;line-height:1.75rem}
  .md\:text-2xl{font-size:1.5rem;line-height:2rem}
  .md\:text-3xl{font-size:1.875rem;line-height:2.25rem}
  .md\:text-4xl{font-size:2.25rem;line-height:2.5rem}
}

@media (min-width:1024px){
  .lg\:text-2xl{font-size:1.5rem;line-height:2rem}
  .lg\:text-3xl{font-size:1.875rem;line-height:2.25rem}
  .lg\:text-4xl{font-size:2.25rem;line-height:2.5rem}
  .lg\:text-5xl{font-size:3rem;line-height:1}
}