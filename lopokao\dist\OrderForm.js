"use strict";

function _typeof(o) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o; }, _typeof(o); }
function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
function _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }
function _toPropertyKey(t) { var i = _toPrimitive(t, "string"); return "symbol" == _typeof(i) ? i : i + ""; }
function _toPrimitive(t, r) { if ("object" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || "default"); if ("object" != _typeof(i)) return i; throw new TypeError("@@toPrimitive must return a primitive value."); } return ("string" === r ? String : Number)(t); }
function _regenerator() { /*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */ var e, t, r = "function" == typeof Symbol ? Symbol : {}, n = r.iterator || "@@iterator", o = r.toStringTag || "@@toStringTag"; function i(r, n, o, i) { var c = n && n.prototype instanceof Generator ? n : Generator, u = Object.create(c.prototype); return _regeneratorDefine2(u, "_invoke", function (r, n, o) { var i, c, u, f = 0, p = o || [], y = !1, G = { p: 0, n: 0, v: e, a: d, f: d.bind(e, 4), d: function d(t, r) { return i = t, c = 0, u = e, G.n = r, a; } }; function d(r, n) { for (c = r, u = n, t = 0; !y && f && !o && t < p.length; t++) { var o, i = p[t], d = G.p, l = i[2]; r > 3 ? (o = l === n) && (c = i[4] || 3, u = i[5] === e ? i[3] : i[5], i[4] = 3, i[5] = e) : i[0] <= d && ((o = r < 2 && d < i[1]) ? (c = 0, G.v = n, G.n = i[1]) : d < l && (o = r < 3 || i[0] > n || n > l) && (i[4] = r, i[5] = n, G.n = l, c = 0)); } if (o || r > 1) return a; throw y = !0, n; } return function (o, p, l) { if (f > 1) throw TypeError("Generator is already running"); for (y && 1 === p && d(p, l), c = p, u = l; (t = c < 2 ? e : u) || !y;) { i || (c ? c < 3 ? (c > 1 && (G.n = -1), d(c, u)) : G.n = u : G.v = u); try { if (f = 2, i) { if (c || (o = "next"), t = i[o]) { if (!(t = t.call(i, u))) throw TypeError("iterator result is not an object"); if (!t.done) return t; u = t.value, c < 2 && (c = 0); } else 1 === c && (t = i["return"]) && t.call(i), c < 2 && (u = TypeError("The iterator does not provide a '" + o + "' method"), c = 1); i = e; } else if ((t = (y = G.n < 0) ? u : r.call(n, G)) !== a) break; } catch (t) { i = e, c = 1, u = t; } finally { f = 1; } } return { value: t, done: y }; }; }(r, o, i), !0), u; } var a = {}; function Generator() {} function GeneratorFunction() {} function GeneratorFunctionPrototype() {} t = Object.getPrototypeOf; var c = [][n] ? t(t([][n]())) : (_regeneratorDefine2(t = {}, n, function () { return this; }), t), u = GeneratorFunctionPrototype.prototype = Generator.prototype = Object.create(c); function f(e) { return Object.setPrototypeOf ? Object.setPrototypeOf(e, GeneratorFunctionPrototype) : (e.__proto__ = GeneratorFunctionPrototype, _regeneratorDefine2(e, o, "GeneratorFunction")), e.prototype = Object.create(u), e; } return GeneratorFunction.prototype = GeneratorFunctionPrototype, _regeneratorDefine2(u, "constructor", GeneratorFunctionPrototype), _regeneratorDefine2(GeneratorFunctionPrototype, "constructor", GeneratorFunction), GeneratorFunction.displayName = "GeneratorFunction", _regeneratorDefine2(GeneratorFunctionPrototype, o, "GeneratorFunction"), _regeneratorDefine2(u), _regeneratorDefine2(u, o, "Generator"), _regeneratorDefine2(u, n, function () { return this; }), _regeneratorDefine2(u, "toString", function () { return "[object Generator]"; }), (_regenerator = function _regenerator() { return { w: i, m: f }; })(); }
function _regeneratorDefine2(e, r, n, t) { var i = Object.defineProperty; try { i({}, "", {}); } catch (e) { i = 0; } _regeneratorDefine2 = function _regeneratorDefine(e, r, n, t) { if (r) i ? i(e, r, { value: n, enumerable: !t, configurable: !t, writable: !t }) : e[r] = n;else { var o = function o(r, n) { _regeneratorDefine2(e, r, function (e) { return this._invoke(r, n, e); }); }; o("next", 0), o("throw", 1), o("return", 2); } }, _regeneratorDefine2(e, r, n, t); }
function asyncGeneratorStep(n, t, e, r, o, a, c) { try { var i = n[a](c), u = i.value; } catch (n) { return void e(n); } i.done ? t(u) : Promise.resolve(u).then(r, o); }
function _asyncToGenerator(n) { return function () { var t = this, e = arguments; return new Promise(function (r, o) { var a = n.apply(t, e); function _next(n) { asyncGeneratorStep(a, r, o, _next, _throw, "next", n); } function _throw(n) { asyncGeneratorStep(a, r, o, _next, _throw, "throw", n); } _next(void 0); }); }; }
function _slicedToArray(r, e) { return _arrayWithHoles(r) || _iterableToArrayLimit(r, e) || _unsupportedIterableToArray(r, e) || _nonIterableRest(); }
function _nonIterableRest() { throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); }
function _unsupportedIterableToArray(r, a) { if (r) { if ("string" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return "Object" === t && r.constructor && (t = r.constructor.name), "Map" === t || "Set" === t ? Array.from(r) : "Arguments" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }
function _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }
function _iterableToArrayLimit(r, l) { var t = null == r ? null : "undefined" != typeof Symbol && r[Symbol.iterator] || r["@@iterator"]; if (null != t) { var e, n, i, u, a = [], f = !0, o = !1; try { if (i = (t = t.call(r)).next, 0 === l) { if (Object(t) !== t) return; f = !1; } else for (; !(f = (e = i.call(t)).done) && (a.push(e.value), a.length !== l); f = !0); } catch (r) { o = !0, n = r; } finally { try { if (!f && null != t["return"] && (u = t["return"](), Object(u) !== u)) return; } finally { if (o) throw n; } } return a; } }
function _arrayWithHoles(r) { if (Array.isArray(r)) return r; }
function OrderForm() {
  try {
    // 檢測是否為 LINE 瀏覽器
    var _React$useState = React.useState(false),
      _React$useState2 = _slicedToArray(_React$useState, 2),
      isLineApp = _React$useState2[0],
      setIsLineApp = _React$useState2[1];

    var _React$useState3 = React.useState({
        products: {
          radish: 0,
          taro: 0,
          hongkong: 0
        },
        shipping: 0,
        totalAmount: 0,
        customerName: '',
        phone: '',
        deliveryMethod: '',
        district: '',
        area: '',
        address: '',
        preferredDate: '',
        preferredTime: '上午 (13點前)',
        contactMethod: '',
        socialAccount: '',
        paymentMethod: '',
        notes: '',
        storeName: '',
        storeAddress: '',
        storeId: ''
      }),
      _React$useState4 = _slicedToArray(_React$useState3, 2),
      formData = _React$useState4[0],
      setFormData = _React$useState4[1];
    var _React$useState5 = React.useState(false),
      _React$useState6 = _slicedToArray(_React$useState5, 2),
      showBankInfo = _React$useState6[0],
      setShowBankInfo = _React$useState6[1];
    var _React$useState7 = React.useState([]),
      _React$useState8 = _slicedToArray(_React$useState7, 2),
      districts = _React$useState8[0],
      setDistricts = _React$useState8[1];
    var _React$useState9 = React.useState(false),
      _React$useState10 = _slicedToArray(_React$useState9, 2),
      showDeliveryAlert = _React$useState10[0],
      setShowDeliveryAlert = _React$useState10[1];
    var _React$useState11 = React.useState(''),
      _React$useState12 = _slicedToArray(_React$useState11, 2),
      deliveryAlertType = _React$useState12[0],
      setDeliveryAlertType = _React$useState12[1];
    var _React$useState13 = React.useState(false),
      _React$useState14 = _slicedToArray(_React$useState13, 2),
      showStorePicker = _React$useState14[0],
      setShowStorePicker = _React$useState14[1];
    var _React$useState15 = React.useState(false),
      _React$useState16 = _slicedToArray(_React$useState15, 2),
      showOrderConfirm = _React$useState16[0],
      setShowOrderConfirm = _React$useState16[1];
    var _React$useState17 = React.useState(''),
      _React$useState18 = _slicedToArray(_React$useState17, 2),
      addressWarning = _React$useState18[0],
      setAddressWarning = _React$useState18[1];
    var modalRef = React.useRef(null);

    // 檢測 LINE 瀏覽器
    React.useEffect(function () {
      var userAgent = navigator.userAgent || '';
      var isLine = userAgent.includes('Line') || userAgent.includes('LINE');
      setIsLineApp(isLine);
      if (isLine) {
        console.log('🔍 檢測到 LINE 瀏覽器，將使用特殊處理邏輯');
      }
    }, []);

    React.useEffect(function () {
      calculateTotal();
      if (formData.district) {
        setDistricts(cityDistricts[formData.district] || []);
      } else {
        // 如果沒有選擇縣市，清空地區選項
        setDistricts([]);
      }
    }, [formData.products, formData.district]);

    // LINE 瀏覽器特殊處理：強制重置地址選擇
    React.useEffect(function () {
      if (isLineApp && formData.district && formData.deliveryMethod !== '宅配到府') {
        console.log('🔧 LINE 瀏覽器：重置地址選擇');
        setFormData(function (prev) {
          return _objectSpread(_objectSpread({}, prev), {}, {
            district: '',
            area: ''
          });
        });
      }
    }, [isLineApp, formData.deliveryMethod]);

    // 可選擇的到貨日期狀態
    var _React$useState19 = React.useState([]),
      _React$useState20 = _slicedToArray(_React$useState19, 2),
      availableDates = _React$useState20[0],
      setAvailableDates = _React$useState20[1];
    var _React$useState21 = React.useState(false),
      _React$useState22 = _slicedToArray(_React$useState21, 2),
      datesLoading = _React$useState22[0],
      setDatesLoading = _React$useState22[1];

    // 從後台 API 獲取可選擇的到貨日期
    var fetchAvailableDates = /*#__PURE__*/function () {
      var _ref = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee(deliveryMethod) {
        var response, result, _t;
        return _regenerator().w(function (_context) {
          while (1) switch (_context.n) {
            case 0:
              _context.p = 0;
              setDatesLoading(true);
              _context.n = 1;
              return fetch("./api/delivery_settings.php?action=available_dates&delivery_method=".concat(encodeURIComponent(deliveryMethod)));
            case 1:
              response = _context.v;
              _context.n = 2;
              return response.json();
            case 2:
              result = _context.v;
              if (result.success) {
                setAvailableDates(result.data);
              } else {
                console.error('獲取可選日期失敗:', result.message);
                setAvailableDates([]);
              }
              _context.n = 4;
              break;
            case 3:
              _context.p = 3;
              _t = _context.v;
              console.error('獲取可選日期錯誤:', _t);
              setAvailableDates([]);
            case 4:
              _context.p = 4;
              setDatesLoading(false);
              return _context.f(4);
            case 5:
              return _context.a(2);
          }
        }, _callee, null, [[0, 3, 4, 5]]);
      }));
      return function fetchAvailableDates(_x) {
        return _ref.apply(this, arguments);
      };
    }();

    // 初始化時載入預設日期（如果沒有選擇配送方式）
    React.useEffect(function () {
      // 頁面載入時，如果已有配送方式則載入對應日期
      if (formData.deliveryMethod) {
        fetchAvailableDates(formData.deliveryMethod);
      }
    }, []); // 只在組件掛載時執行一次

    // 產品數量控制函數
    var updateProductQuantity = function updateProductQuantity(product, change) {
      var currentQty = parseInt(formData.products[product]) || 0;
      var newQty = Math.max(0, currentQty + change);
      setFormData(_objectSpread(_objectSpread({}, formData), {}, {
        products: _objectSpread(_objectSpread({}, formData.products), {}, _defineProperty({}, product, newQty))
      }));
    };
    var calculateTotal = function calculateTotal() {
      var radishTotal = (parseInt(formData.products.radish) || 0) * 250;
      var taroTotal = (parseInt(formData.products.taro) || 0) * 350;
      var hongkongTotal = (parseInt(formData.products.hongkong) || 0) * 350;
      var subtotal = radishTotal + taroTotal + hongkongTotal;
      var shipping = 0;
      if (subtotal > 0 && subtotal < 350) {
        shipping = 100;
      }
      setFormData(function (prev) {
        return _objectSpread(_objectSpread({}, prev), {}, {
          shipping: shipping,
          totalAmount: subtotal + shipping
        });
      });
    };
    var handleDeliveryMethodChange = function handleDeliveryMethodChange(e) {
      var method = e.target.value;
      setFormData(_objectSpread(_objectSpread({}, formData), {}, {
        deliveryMethod: method,
        storeName: '',
        storeAddress: '',
        storeId: '',
        preferredDate: ''
      }));

      // 清除地址警示訊息
      setAddressWarning('');
      if (method === '宅配到府') {
        setDeliveryAlertType('home');
        setShowDeliveryAlert(true);
      } else if (method === '7-11門市') {
        setDeliveryAlertType('store');
        setShowDeliveryAlert(true);
      }

      // 根據配送方式獲取可選日期
      if (method) {
        fetchAvailableDates(method);
      }
    };

    // 門市選擇相關函數
    var openStorePicker = function openStorePicker() {
      setShowStorePicker(true);
    };
    var closeStorePicker = function closeStorePicker() {
      setShowStorePicker(false);
    };
    var setStoreInfo = function setStoreInfo(name, address, id) {
      setFormData(function (prev) {
        return _objectSpread(_objectSpread({}, prev), {}, {
          storeName: name,
          storeAddress: address,
          storeId: id
        });
      });
    };

    // 全域函數設定（供iframe使用）
    React.useEffect(function () {
      window.setStoreInfo = setStoreInfo;
      window.closeStorePicker = closeStorePicker;
    }, []);

    // 動態設定彈窗高度以適應行動裝置瀏覽器
    React.useEffect(function () {
      var setModalHeight = function setModalHeight() {
        if (modalRef.current) {
          // 減去上下邊距 (p-4 = 1rem * 2 = 32px, 額外再多留一些空間)
          var availableHeight = window.innerHeight - 80;
          modalRef.current.style.maxHeight = "".concat(availableHeight, "px");
        }
      };
      if (showOrderConfirm) {
        setModalHeight();
        window.addEventListener('resize', setModalHeight);
        window.addEventListener('orientationchange', setModalHeight);
      }
      return function () {
        window.removeEventListener('resize', setModalHeight);
        window.removeEventListener('orientationchange', setModalHeight);
      };
    }, [showOrderConfirm]);
    var handlePaymentMethodChange = function handlePaymentMethodChange(e) {
      var method = e.target.value;
      setFormData(_objectSpread(_objectSpread({}, formData), {}, {
        paymentMethod: method
      }));
      setShowBankInfo(method === '銀行轉帳');
    };

    // 地址驗證處理函數
    var handleAddressChange = function handleAddressChange(e) {
      var address = e.target.value;
      setFormData(_objectSpread(_objectSpread({}, formData), {}, {
        address: address
      }));

      // 即時檢查地址格式
      if (address.trim() && formData.deliveryMethod === '宅配到府') {
        var hasNumber = /\d+號/.test(address);
        if (!hasNumber && address.length > 3) {
          setAddressWarning('⚠️地址須包含門牌號碼或數字後方有(號)字');
        } else {
          setAddressWarning('');
        }
      } else {
        setAddressWarning('');
      }
    };
    var _React$useState23 = React.useState(false),
      _React$useState24 = _slicedToArray(_React$useState23, 2),
      isSubmitting = _React$useState24[0],
      setIsSubmitting = _React$useState24[1];
    var handleSubmit = /*#__PURE__*/function () {
      var _ref2 = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee2(e) {
        return _regenerator().w(function (_context2) {
          while (1) switch (_context2.n) {
            case 0:
              e.preventDefault();
              if (!isSubmitting) {
                _context2.n = 1;
                break;
              }
              return _context2.a(2);
            case 1:
              if (validateOrderData()) {
                _context2.n = 2;
                break;
              }
              return _context2.a(2);
            case 2:
              // 顯示訂單確認彈窗
              setShowOrderConfirm(true);
            case 3:
              return _context2.a(2);
          }
        }, _callee2);
      }));
      return function handleSubmit(_x2) {
        return _ref2.apply(this, arguments);
      };
    }();
    var confirmSubmitOrder = /*#__PURE__*/function () {
      var _ref3 = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee3() {
        var submitButton, originalText, result, detailedMessage, errorDetails, _submitButton, _t2;
        return _regenerator().w(function (_context3) {
          while (1) switch (_context3.n) {
            case 0:
              setIsSubmitting(true);
              setShowOrderConfirm(false);
              _context3.p = 1;
              // 顯示提交中狀態
              submitButton = document.querySelector('button[type="submit"]');
              if (submitButton) {
                originalText = submitButton.textContent;
                submitButton.textContent = '⏳ 處理中...';
                submitButton.disabled = true;
              }

              // 提交訂單到 Google Sheets
              _context3.n = 2;
              return submitOrderToSheets(formData);
            case 2:
              result = _context3.v;
              if (result.success) {
                // 顯示成功訊息
                detailedMessage = showDetailedResults(result);
                alert(detailedMessage);

                // 重新載入頁面
                window.location.reload();
              } else {
                // 顯示錯誤訊息
                errorDetails = getErrorDetails(result);
                alert(errorDetails);
              }
              _context3.n = 4;
              break;
            case 3:
              _context3.p = 3;
              _t2 = _context3.v;
              console.error('訂單提交系統錯誤：', _t2);
              alert('❌ 系統發生未預期的錯誤，請稍後再試。\n\n錯誤詳情：' + _t2.message);
            case 4:
              _context3.p = 4;
              setIsSubmitting(false);

              // 恢復按鈕狀態
              _submitButton = document.querySelector('button[type="submit"]');
              if (_submitButton) {
                _submitButton.textContent = '確認送出訂單';
                _submitButton.disabled = false;
              }
              return _context3.f(4);
            case 5:
              return _context3.a(2);
          }
        }, _callee3, null, [[1, 3, 4, 5]]);
      }));
      return function confirmSubmitOrder() {
        return _ref3.apply(this, arguments);
      };
    }();
    var validateOrderData = function validateOrderData() {
      // 檢查必填欄位
      var requiredFields = [{
        field: 'customerName',
        name: '姓名'
      }, {
        field: 'phone',
        name: '電話'
      }, {
        field: 'deliveryMethod',
        name: '配送方式'
      }, {
        field: 'preferredDate',
        name: '希望到貨日期'
      }, {
        field: 'contactMethod',
        name: '聯繫方式'
      }, {
        field: 'paymentMethod',
        name: '付款方式'
      }];
      for (var _i = 0, _requiredFields = requiredFields; _i < _requiredFields.length; _i++) {
        var _requiredFields$_i = _requiredFields[_i],
          field = _requiredFields$_i.field,
          name = _requiredFields$_i.name;
        if (!formData[field] || formData[field].trim() === '') {
          alert("\u274C \u8ACB\u586B\u5BEB".concat(name));
          return false;
        }
      }

      // 檢查配送方式相關欄位
      if (formData.deliveryMethod === '宅配到府') {
        if (!formData.district || !formData.area || !formData.address) {
          alert('❌ 宅配到府請填寫完整地址資訊');
          return false;
        }

        // 檢查地址是否包含門牌號碼
        var addressText = formData.address.trim();
        var hasNumber = /\d+號/.test(addressText);
        if (!hasNumber) {
          alert('❌ 地址須含門牌號碼或數字後方有(號)字');
          return false;
        }
      } else if (formData.deliveryMethod === '7-11門市') {
        if (!formData.storeName || !formData.storeAddress) {
          alert('❌ 請選擇7-11取貨門市');
          return false;
        }
      }

      // 檢查產品數量
      var totalQuantity = (parseInt(formData.products.radish) || 0) + (parseInt(formData.products.taro) || 0) + (parseInt(formData.products.hongkong) || 0);
      if (totalQuantity <= 0) {
        alert('❌ 請至少選擇一項商品');
        return false;
      }

      // 檢查電話號碼格式
      // 檢查電話號碼格式
      var cleanPhone = formData.phone.replace(/\D/g, '');
      var phoneRegex = /^(09\d{8}|(0[2-8])\d{7,})$/;
      if (!phoneRegex.test(cleanPhone)) {
        alert('❌ 電話號碼格式不正確。\n\n手機請輸入10碼 (如 0912345678)\n市話請輸入區碼+號碼 (如 0229998888)');
        return false;
      }
      return true;
    };
    return /*#__PURE__*/React.createElement("form", {
      onSubmit: handleSubmit,
      className: "bg-white p-6 rounded-xl shadow-xl hover:shadow-2xl transition-all duration-300 space-y-4",
      "data-name": "order-form"
    }, /*#__PURE__*/React.createElement("h3", {
      className: "text-xl font-bold mb-4"
    }, "\u8A02\u8CFC\u8868\u55AE"), /*#__PURE__*/React.createElement("div", {
      className: "space-y-4",
      "data-name": "product-selection"
    }, /*#__PURE__*/React.createElement("div", {
      className: "flex items-center justify-between p-3 border rounded-lg"
    }, /*#__PURE__*/React.createElement("span", {
      className: "text-gray-700 font-medium flex-1"
    }, "\u539F\u5473\u863F\u8514\u7CD5 (NT$ 250/\u689D)"), /*#__PURE__*/React.createElement("div", {
      className: "flex items-center space-x-2 flex-shrink-0"
    }, /*#__PURE__*/React.createElement("button", {
      type: "button",
      onClick: function onClick() {
        return updateProductQuantity('radish', -1);
      },
      className: "w-6 h-6 bg-gray-200 hover:bg-gray-300 rounded-full flex items-center justify-center text-sm font-bold transition-colors"
    }, "-"), /*#__PURE__*/React.createElement("span", {
      className: "w-6 text-center font-semibold text-sm"
    }, formData.products.radish), /*#__PURE__*/React.createElement("button", {
      type: "button",
      onClick: function onClick() {
        return updateProductQuantity('radish', 1);
      },
      className: "w-6 h-6 bg-red-500 hover:bg-red-600 text-white rounded-full flex items-center justify-center text-sm font-bold transition-colors"
    }, "+"))), /*#__PURE__*/React.createElement("div", {
      className: "flex items-center justify-between p-3 border rounded-lg"
    }, /*#__PURE__*/React.createElement("span", {
      className: "text-gray-700 font-medium flex-1"
    }, "\u828B\u982D\u7CBF (NT$ 350/\u689D)"), /*#__PURE__*/React.createElement("div", {
      className: "flex items-center space-x-2 flex-shrink-0"
    }, /*#__PURE__*/React.createElement("button", {
      type: "button",
      onClick: function onClick() {
        return updateProductQuantity('taro', -1);
      },
      className: "w-6 h-6 bg-gray-200 hover:bg-gray-300 rounded-full flex items-center justify-center text-sm font-bold transition-colors"
    }, "-"), /*#__PURE__*/React.createElement("span", {
      className: "w-6 text-center font-semibold text-sm"
    }, formData.products.taro), /*#__PURE__*/React.createElement("button", {
      type: "button",
      onClick: function onClick() {
        return updateProductQuantity('taro', 1);
      },
      className: "w-6 h-6 bg-red-500 hover:bg-red-600 text-white rounded-full flex items-center justify-center text-sm font-bold transition-colors"
    }, "+"))), /*#__PURE__*/React.createElement("div", {
      className: "flex items-center justify-between p-3 border rounded-lg"
    }, /*#__PURE__*/React.createElement("span", {
      className: "text-gray-700 font-medium flex-1"
    }, "\u53F0\u5F0F\u9E79\u863F\u8514\u7CD5 (NT$ 350/\u689D)"), /*#__PURE__*/React.createElement("div", {
      className: "flex items-center space-x-2 flex-shrink-0"
    }, /*#__PURE__*/React.createElement("button", {
      type: "button",
      onClick: function onClick() {
        return updateProductQuantity('hongkong', -1);
      },
      className: "w-6 h-6 bg-gray-200 hover:bg-gray-300 rounded-full flex items-center justify-center text-sm font-bold transition-colors"
    }, "-"), /*#__PURE__*/React.createElement("span", {
      className: "w-6 text-center font-semibold text-sm"
    }, formData.products.hongkong), /*#__PURE__*/React.createElement("button", {
      type: "button",
      onClick: function onClick() {
        return updateProductQuantity('hongkong', 1);
      },
      className: "w-6 h-6 bg-red-500 hover:bg-red-600 text-white rounded-full flex items-center justify-center text-sm font-bold transition-colors"
    }, "+")))), /*#__PURE__*/React.createElement("div", {
      className: "bg-gradient-to-br from-gray-50 to-gray-100 p-4 rounded-xl shadow-lg",
      "data-name": "order-summary"
    }, /*#__PURE__*/React.createElement("h4", {
      className: "font-bold mb-2"
    }, "\u8A02\u55AE\u6458\u8981"), /*#__PURE__*/React.createElement("div", {
      className: "grid grid-cols-2 gap-2"
    }, /*#__PURE__*/React.createElement("p", null, "\u5546\u54C1\u5C0F\u8A08\uFF1A"), /*#__PURE__*/React.createElement("p", {
      className: "text-right"
    }, "NT$ ", formData.totalAmount - formData.shipping), /*#__PURE__*/React.createElement("p", null, "\u904B\u8CBB\uFF1A"), /*#__PURE__*/React.createElement("p", {
      className: "text-right"
    }, "NT$ ", formData.shipping), /*#__PURE__*/React.createElement("p", {
      className: "font-bold"
    }, "\u7E3D\u8A08\uFF1A"), /*#__PURE__*/React.createElement("p", {
      className: "text-right font-bold",
      style: {
        fontSize: '18px',
        color: '#dc2626'
      }
    }, "NT$ ", formData.totalAmount))), /*#__PURE__*/React.createElement("div", {
      className: "grid grid-cols-1 md:grid-cols-2 gap-4",
      "data-name": "customer-info",
      style: {
        fontSize: '16px'
      }
    }, /*#__PURE__*/React.createElement("div", null, /*#__PURE__*/React.createElement("label", {
      className: "block text-gray-700 mb-2"
    }, "\u59D3\u540D"), /*#__PURE__*/React.createElement("input", {
      type: "text",
      className: "w-full px-2 py-0.5 border rounded text-sm",
      required: true,
      value: formData.customerName,
      onChange: function onChange(e) {
        return setFormData(_objectSpread(_objectSpread({}, formData), {}, {
          customerName: e.target.value
        }));
      }
    })), /*#__PURE__*/React.createElement("div", null, /*#__PURE__*/React.createElement("label", {
      className: "block text-gray-700 mb-2"
    }, "\u96FB\u8A71"), /*#__PURE__*/React.createElement("input", {
      type: "tel",
      className: "w-full px-2 py-0.5 border rounded text-sm",
      required: true,
      value: formData.phone,
      onChange: function onChange(e) {
        return setFormData(_objectSpread(_objectSpread({}, formData), {}, {
          phone: e.target.value
        }));
      }
    })), /*#__PURE__*/React.createElement("div", null, /*#__PURE__*/React.createElement("label", {
      className: "block text-gray-700 mb-2"
    }, "\u914D\u9001\u65B9\u5F0F"), /*#__PURE__*/React.createElement("select", {
      className: "w-full px-2 py-1.5 border rounded text-sm",
      required: true,
      value: formData.deliveryMethod,
      onChange: handleDeliveryMethodChange
    }, /*#__PURE__*/React.createElement("option", {
      value: ""
    }, "\u8ACB\u9078\u64C7\u914D\u9001\u65B9\u5F0F"), /*#__PURE__*/React.createElement("option", {
      value: "\u5B85\u914D\u5230\u5E9C"
    }, "\u5B85\u914D\u5230\u5E9C"), /*#__PURE__*/React.createElement("option", {
      value: "7-11\u9580\u5E02"
    }, "\u8D85\u5546\u53D6\u8CA8 (7-11)"))), formData.deliveryMethod === '宅配到府' && /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement("div", {
      className: "grid grid-cols-2 gap-2"
    }, /*#__PURE__*/React.createElement("div", null, /*#__PURE__*/React.createElement("label", {
      className: "block text-gray-700 mb-2"
    }, "\u7E23\u5E02"), /*#__PURE__*/React.createElement("select", {
      className: "w-full px-2 py-1.5 border rounded text-sm",
      required: true,
      value: formData.district,
      onChange: function onChange(e) {
        return setFormData(_objectSpread(_objectSpread({}, formData), {}, {
          district: e.target.value,
          area: ''
        }));
      },
      key: "city-select-" + isLineApp // 強制重新渲染
    }, /*#__PURE__*/React.createElement("option", {
      value: "",
      disabled: isLineApp,
      selected: !formData.district
    }, "\u8ACB\u9078\u64C7\u7E23\u5E02"), Object.keys(cityDistricts).map(function (city) {
      return /*#__PURE__*/React.createElement("option", {
        key: city,
        value: city
      }, city);
    })), isLineApp && /*#__PURE__*/React.createElement("p", {
      className: "text-xs text-blue-600 mt-1"
    }, "\uD83D\uDCA1 LINE \u700F\u89BD\u5668\u7528\u6236\uFF1A\u8ACB\u9EDE\u64CA\u4E0B\u62C9\u9078\u55AE\u9078\u64C7\u7E23\u5E02")), /*#__PURE__*/React.createElement("div", null, /*#__PURE__*/React.createElement("label", {
      className: "block text-gray-700 mb-2"
    }, "\u5730\u5340"), /*#__PURE__*/React.createElement("select", {
      className: "w-full px-2 py-1.5 border rounded text-sm",
      required: true,
      value: formData.area,
      onChange: function onChange(e) {
        return setFormData(_objectSpread(_objectSpread({}, formData), {}, {
          area: e.target.value
        }));
      }
    }, /*#__PURE__*/React.createElement("option", {
      value: ""
    }, "\u8ACB\u9078\u64C7\u5730\u5340"), districts.map(function (area) {
      return /*#__PURE__*/React.createElement("option", {
        key: area,
        value: area
      }, area);
    })))), /*#__PURE__*/React.createElement("div", {
      className: "md:col-span-2"
    }, /*#__PURE__*/React.createElement("label", {
      className: "block text-gray-700 mb-2"
    }, "\u5730\u5740"), /*#__PURE__*/React.createElement("input", {
      type: "text",
      className: "w-full px-2 py-1.5 border rounded text-sm ".concat(addressWarning ? 'border-orange-400' : ''),
      required: true,
      placeholder: "\u8ACB\u8F38\u5165\u5B8C\u6574\u5730\u5740\uFF08\u4F8B\u5982\uFF1A\u4E2D\u6B63\u8DEF123\u865F\uFF09",
      value: formData.address,
      onChange: handleAddressChange
    }), addressWarning && /*#__PURE__*/React.createElement("p", {
      className: "text-orange-600 text-xs mt-1 flex items-center"
    }, /*#__PURE__*/React.createElement("span", {
      className: "mr-1"
    }, "\u26A0\uFE0F"), addressWarning))), formData.deliveryMethod === '7-11門市' && /*#__PURE__*/React.createElement("div", {
      className: "md:col-span-2"
    }, /*#__PURE__*/React.createElement("div", {
      className: "flex justify-between items-center mb-2"
    }, /*#__PURE__*/React.createElement("label", {
      className: "block text-gray-700"
    }, "\u9078\u64C7\u53D6\u8CA8\u9580\u5E02"), /*#__PURE__*/React.createElement("button", {
      type: "button",
      onClick: openStorePicker,
      className: "bg-blue-500 hover:bg-blue-600 text-white px-2 py-0.5 rounded text-xs transition-colors"
    }, "\u9078\u64C77-11\u9580\u5E02")), /*#__PURE__*/React.createElement("div", {
      className: "border rounded p-3 bg-gray-50"
    }, formData.storeName ? /*#__PURE__*/React.createElement("div", null, /*#__PURE__*/React.createElement("p", {
      className: "font-semibold text-green-600",
      style: {
        fontSize: '16px'
      }
    }, "\u2713 \u5DF2\u9078\u64C7\u9580\u5E02"), /*#__PURE__*/React.createElement("p", {
      style: {
        fontSize: '16px'
      }
    }, formData.storeName, " \u9580\u5E02"), /*#__PURE__*/React.createElement("p", {
      className: "text-gray-600",
      style: {
        fontSize: '14px'
      }
    }, formData.storeAddress), /*#__PURE__*/React.createElement("p", {
      className: "text-gray-600",
      style: {
        fontSize: '14px'
      }
    }, "\u5E97\u865F\uFF1A", formData.storeId)) : /*#__PURE__*/React.createElement("p", {
      className: "text-gray-500"
    }, "\u5C1A\u672A\u9078\u64C7\u9580\u5E02"))), /*#__PURE__*/React.createElement("div", null, /*#__PURE__*/React.createElement("label", {
      className: "block text-gray-700 mb-2"
    }, "\u5E0C\u671B\u5230\u8CA8\u65E5\u671F"), /*#__PURE__*/React.createElement("select", {
      className: "w-full px-2 py-1.5 border rounded text-sm",
      required: true,
      value: formData.preferredDate,
      onChange: function onChange(e) {
        return setFormData(_objectSpread(_objectSpread({}, formData), {}, {
          preferredDate: e.target.value
        }));
      },
      disabled: datesLoading || !formData.deliveryMethod
    }, /*#__PURE__*/React.createElement("option", {
      value: ""
    }, datesLoading ? '載入中...' : !formData.deliveryMethod ? '請先選擇配送方式' : '請選擇到貨日期'), availableDates.map(function (date) {
      return /*#__PURE__*/React.createElement("option", {
        key: date.value,
        value: date.value
      }, date.display);
    }))), /*#__PURE__*/React.createElement("div", null, /*#__PURE__*/React.createElement("label", {
      className: "block text-gray-700 mb-2"
    }, "\u5E0C\u671B\u5230\u8CA8\u6642\u9593"), /*#__PURE__*/React.createElement("select", {
      className: "w-full px-2 py-1.5 border rounded text-sm",
      required: true,
      value: formData.preferredTime,
      onChange: function onChange(e) {
        return setFormData(_objectSpread(_objectSpread({}, formData), {}, {
          preferredTime: e.target.value
        }));
      }
    }, /*#__PURE__*/React.createElement("option", {
      value: "\u4E0A\u5348"
    }, "\u4E0A\u5348 (13\u9EDE\u524D)"), /*#__PURE__*/React.createElement("option", {
      value: "\u4E0B\u5348"
    }, "\u4E0B\u5348 (13-18\u9EDE)"))), /*#__PURE__*/React.createElement("div", null, /*#__PURE__*/React.createElement("label", {
      className: "block text-gray-700 mb-2"
    }, "\u900F\u904E\u4EC0\u9EBC\u806F\u7E6B\u8CE3\u5BB6"), /*#__PURE__*/React.createElement("select", {
      className: "w-full px-2 py-1.5 border rounded text-sm",
      required: true,
      value: formData.contactMethod,
      onChange: function onChange(e) {
        return setFormData(_objectSpread(_objectSpread({}, formData), {}, {
          contactMethod: e.target.value
        }));
      }
    }, /*#__PURE__*/React.createElement("option", {
      value: ""
    }, "\u8ACB\u9078\u64C7\u806F\u7E6B\u65B9\u5F0F"), /*#__PURE__*/React.createElement("option", {
      value: "facebook"
    }, "Facebook"), /*#__PURE__*/React.createElement("option", {
      value: "line"
    }, "Line"))), /*#__PURE__*/React.createElement("div", null, /*#__PURE__*/React.createElement("label", {
      className: "block text-gray-700 mb-2"
    }, "\u793E\u7FA4\u5E33\u865F\u540D\u7A31"), /*#__PURE__*/React.createElement("input", {
      type: "text",
      className: "w-full px-2 py-0.5 border rounded text-sm",
      placeholder: "\u8ACB\u8F38\u5165\u60A8\u7684FB/Line\u540D\u7A31",
      value: formData.socialAccount,
      onChange: function onChange(e) {
        return setFormData(_objectSpread(_objectSpread({}, formData), {}, {
          socialAccount: e.target.value
        }));
      }
    })), /*#__PURE__*/React.createElement("div", null, /*#__PURE__*/React.createElement("label", {
      className: "block text-gray-700 mb-2"
    }, "\u4ED8\u6B3E\u65B9\u5F0F"), /*#__PURE__*/React.createElement("select", {
      className: "w-full px-2 py-1.5 border rounded text-sm",
      required: true,
      value: formData.paymentMethod,
      onChange: handlePaymentMethodChange
    }, /*#__PURE__*/React.createElement("option", {
      value: ""
    }, "\u8ACB\u9078\u64C7\u4ED8\u6B3E\u65B9\u5F0F"), /*#__PURE__*/React.createElement("option", {
      value: "\u9280\u884C\u8F49\u5E33"
    }, "\u9280\u884C\u8F49\u5E33"), /*#__PURE__*/React.createElement("option", {
      value: "\u8CA8\u5230\u4ED8\u6B3E"
    }, "\u8CA8\u5230\u4ED8\u6B3E"))), showBankInfo && /*#__PURE__*/React.createElement("div", {
      className: "md:col-span-2 p-3 sm:p-4 bg-blue-50 rounded"
    }, /*#__PURE__*/React.createElement("h4", {
      className: "font-bold mb-1 sm:mb-2"
    }, "\u9280\u884C\u5E33\u6236\u8CC7\u8A0A"), /*#__PURE__*/React.createElement("p", {
      className: "leading-tight sm:leading-normal"
    }, "\u9280\u884C\uFF1A\u4E2D\u570B\u4FE1\u8A17 (\u4EE3\u78BC\uFF1A822)"), /*#__PURE__*/React.createElement("p", {
      className: "leading-tight sm:leading-normal"
    }, "\u5E33\u865F\uFF1A************")), /*#__PURE__*/React.createElement("div", {
      className: "md:col-span-2"
    }, /*#__PURE__*/React.createElement("label", {
      className: "block text-gray-700 mb-2"
    }, "\u5099\u8A3B"), /*#__PURE__*/React.createElement("textarea", {
      className: "w-full px-2 py-1.5 border rounded text-sm",
      rows: "3",
      value: formData.notes,
      onChange: function onChange(e) {
        return setFormData(_objectSpread(_objectSpread({}, formData), {}, {
          notes: e.target.value
        }));
      }
    }))), /*#__PURE__*/React.createElement("button", {
      type: "submit",
      disabled: isSubmitting,
      className: "w-full px-6 py-3 text-white rounded-md transition-colors ".concat(isSubmitting ? 'bg-gray-400 cursor-not-allowed' : 'bg-red-600 hover:bg-red-700'),
      "data-name": "submit-button"
    }, isSubmitting ? '⏳ 處理中...' : '確認送出訂單'), isSubmitting && /*#__PURE__*/React.createElement("div", {
      className: "text-center text-sm mt-2"
    }, /*#__PURE__*/React.createElement("div", {
      className: "flex items-center justify-center space-x-2"
    }, /*#__PURE__*/React.createElement("div", {
      className: "animate-spin rounded-full h-4 w-4 border-b-2 border-red-600"
    }), /*#__PURE__*/React.createElement("span", {
      className: "text-red-600 font-medium"
    }, "\u63D0\u4EA4\u4E2D..."))), showDeliveryAlert && /*#__PURE__*/React.createElement("div", {
      className: "fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
    }, /*#__PURE__*/React.createElement("div", {
      className: "bg-white rounded-lg p-6 max-w-md mx-4"
    }, /*#__PURE__*/React.createElement("h3", {
      className: "text-lg font-bold mb-4"
    }, deliveryAlertType === 'home' ? '宅配到府注意事項' : '超商取貨(7-11)注意事項'), /*#__PURE__*/React.createElement("div", {
      className: "mb-4"
    }, deliveryAlertType === 'home' ? /*#__PURE__*/React.createElement("div", {
      className: "space-y-1 sm:space-y-2"
    }, /*#__PURE__*/React.createElement("div", {
      className: "flex items-center space-x-2"
    }, /*#__PURE__*/React.createElement("span", {
      className: "text-2xl"
    }, "\uD83C\uDFE0"), /*#__PURE__*/React.createElement("p", {
      className: "text-sm leading-tight sm:leading-normal"
    }, "\u8ACB\u52D9\u5FC5\u586B\u5BEB\u6B63\u78BA\u7684\u6536\u4EF6\u5730\u5740\u548C\u96FB\u8A71")), /*#__PURE__*/React.createElement("div", {
      className: "flex items-center space-x-2"
    }, /*#__PURE__*/React.createElement("span", {
      className: "text-2xl"
    }, "\u23F0"), /*#__PURE__*/React.createElement("p", {
      className: "text-sm leading-tight sm:leading-normal"
    }, "\u914D\u9001\u6642\u9593\u53EF\u80FD\u6703\u67091-2\u5C0F\u6642\u7684\u8AA4\u5DEE")), /*#__PURE__*/React.createElement("div", {
      className: "flex items-center space-x-2"
    }, /*#__PURE__*/React.createElement("span", {
      className: "text-2xl"
    }, "\uD83D\uDCE6"), /*#__PURE__*/React.createElement("p", {
      className: "text-sm leading-tight sm:leading-normal"
    }, "\u82E5\u5230\u8CA8\u6642\u7121\u6CD5\u6536\u8CA8\uFF0C\u53EF\u8ACB\u5B85\u914D\u4EBA\u54E1\u6539\u7D04\u6642\u9593\u6216\u5BC4\u653E\u9644\u8FD17-11\u9580\u5E02\u53D6\u8CA8"))) : /*#__PURE__*/React.createElement("div", {
      className: "space-y-1 sm:space-y-2"
    }, /*#__PURE__*/React.createElement("div", {
      className: "flex items-center space-x-2"
    }, /*#__PURE__*/React.createElement("span", {
      className: "text-2xl"
    }, "\uD83D\uDCF1"), /*#__PURE__*/React.createElement("p", {
      className: "text-sm leading-tight sm:leading-normal"
    }, "\u5305\u88F9\u5230\u8D85\u5546\u6703\u6709\u624B\u6A5F\u7C21\u8A0A\u901A\u77E5\uFF0C\u5546\u54C1\u53EA\u4FDD\u75595\u5929\u8ACB\u5118\u901F\u53D6\u8CA8")), /*#__PURE__*/React.createElement("div", {
      className: "flex items-center space-x-2"
    }, /*#__PURE__*/React.createElement("span", {
      className: "text-2xl"
    }, "\u2744\uFE0F"), /*#__PURE__*/React.createElement("p", {
      className: "text-sm leading-tight sm:leading-normal"
    }, "\u7522\u54C1\u53EA\u80FD\u51B7\u85CF\u4E0D\u80FD\u51B7\u51CD\uFF0C\u9818\u8CA8\u6642\u82E5\u662F\u51B7\u51CD\u8ACB\u99AC\u4E0A\u806F\u7D61\u6211\u5011\u6216\u62D2\u6536")), /*#__PURE__*/React.createElement("div", {
      className: "flex items-center space-x-2"
    }, /*#__PURE__*/React.createElement("span", {
      className: "text-2xl"
    }, "\uD83D\uDCE6"), /*#__PURE__*/React.createElement("p", {
      className: "text-sm leading-tight sm:leading-normal"
    }, "\u51FA\u8CA8\u5F8C2\u65E5\u5167\u672A\u6536\u5230\u8D85\u5546\u5230\u8CA8\u7C21\u8A0A\uFF0C\u8ACB\u4E3B\u52D5\u81F4\u96FB\u9580\u5E02\u8A62\u554F")))), /*#__PURE__*/React.createElement("button", {
      onClick: function onClick() {
        return setShowDeliveryAlert(false);
      },
      className: "w-full bg-red-600 hover:bg-red-700 text-white py-2 rounded transition-colors"
    }, "\u6211\u77E5\u9053\u4E86"))), showStorePicker && /*#__PURE__*/React.createElement("div", {
      className: "fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
    }, /*#__PURE__*/React.createElement("div", {
      className: "bg-white rounded-lg w-full max-w-4xl mx-4 h-5/6 flex flex-col"
    }, /*#__PURE__*/React.createElement("div", {
      className: "flex justify-between items-center p-4 border-b"
    }, /*#__PURE__*/React.createElement("h3", {
      className: "text-lg font-bold"
    }, "\u9078\u64C77-11\u9580\u5E02"), /*#__PURE__*/React.createElement("button", {
      onClick: closeStorePicker,
      className: "text-gray-500 hover:text-gray-700 text-2xl"
    }, "\xD7")), /*#__PURE__*/React.createElement("div", {
      className: "flex-1 overflow-hidden"
    }, /*#__PURE__*/React.createElement("iframe", {
      src: "store-selector.php",
      className: "w-full h-full border-0",
      title: "\u9580\u5E02\u9078\u64C7\u5668"
    })))), showOrderConfirm && /*#__PURE__*/React.createElement("div", {
      className: "fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4 order-confirm-modal"
    }, /*#__PURE__*/React.createElement("div", {
      ref: modalRef,
      className: "bg-white rounded-lg w-full flex flex-col",
      style: {
        width: window.innerWidth >= 768 ? '500px' : '90vw'
      }
    }, /*#__PURE__*/React.createElement("h3", {
      className: "text-lg font-bold p-6 pb-4 flex-shrink-0"
    }, "\u78BA\u8A8D\u8A02\u55AE\u5167\u5BB9"), /*#__PURE__*/React.createElement("div", {
      className: "overflow-y-auto px-6 space-y-3 modal-scroll-content",
      style: {
        fontSize: '15px'
      }
    }, /*#__PURE__*/React.createElement("div", {
      className: "border-b pb-2"
    }, /*#__PURE__*/React.createElement("h4", {
      className: "font-semibold mb-2"
    }, "\u8A02\u8CFC\u5546\u54C1"), formData.products.radish > 0 && /*#__PURE__*/React.createElement("p", null, "\u539F\u5473\u863F\u8514\u7CD5\uFF1A", formData.products.radish, " \u689D (NT$ ", formData.products.radish * 250, ")"), formData.products.taro > 0 && /*#__PURE__*/React.createElement("p", null, "\u828B\u982D\u7CBF\uFF1A", formData.products.taro, " \u689D (NT$ ", formData.products.taro * 350, ")"), formData.products.hongkong > 0 && /*#__PURE__*/React.createElement("p", null, "\u53F0\u5F0F\u9E79\u863F\u8514\u7CD5\uFF1A", formData.products.hongkong, " \u689D (NT$ ", formData.products.hongkong * 350, ")"), /*#__PURE__*/React.createElement("p", {
      className: "font-semibold mt-2"
    }, "\u904B\u8CBB\uFF1ANT$ ", formData.shipping), /*#__PURE__*/React.createElement("p", {
      className: "font-bold text-lg",
      style: {
        color: '#dc2626'
      }
    }, "\u7E3D\u8A08\uFF1ANT$ ", formData.totalAmount)), /*#__PURE__*/React.createElement("div", {
      className: "border-b pb-2"
    }, /*#__PURE__*/React.createElement("h4", {
      className: "font-semibold mb-2"
    }, "\u5BA2\u6236\u8CC7\u8A0A"), /*#__PURE__*/React.createElement("p", null, "\u59D3\u540D\uFF1A", formData.customerName), /*#__PURE__*/React.createElement("p", null, "\u96FB\u8A71\uFF1A", formData.phone), /*#__PURE__*/React.createElement("p", null, "\u806F\u7E6B\u65B9\u5F0F\uFF1A", formData.contactMethod), /*#__PURE__*/React.createElement("p", null, "\u793E\u7FA4\u5E33\u865F\uFF1A", formData.socialAccount)), /*#__PURE__*/React.createElement("div", {
      className: "border-b pb-2"
    }, /*#__PURE__*/React.createElement("h4", {
      className: "font-semibold mb-2"
    }, "\u914D\u9001\u8CC7\u8A0A"), /*#__PURE__*/React.createElement("p", null, "\u914D\u9001\u65B9\u5F0F\uFF1A", formData.deliveryMethod), formData.deliveryMethod === '宅配到府' ? /*#__PURE__*/React.createElement("p", null, "\u5730\u5740\uFF1A", formData.district, " ", formData.area, " ", formData.address) : /*#__PURE__*/React.createElement("div", null, /*#__PURE__*/React.createElement("p", null, "\u53D6\u8CA8\u9580\u5E02\uFF1A", formData.storeName, " \u9580\u5E02"), /*#__PURE__*/React.createElement("p", null, "\u9580\u5E02\u5730\u5740\uFF1A", formData.storeAddress), /*#__PURE__*/React.createElement("p", null, "\u5E97\u865F\uFF1A", formData.storeId)), /*#__PURE__*/React.createElement("p", null, "\u5E0C\u671B\u5230\u8CA8\u65E5\u671F\uFF1A", formData.preferredDate), /*#__PURE__*/React.createElement("p", null, "\u5E0C\u671B\u5230\u8CA8\u6642\u9593\uFF1A", formData.preferredTime)), /*#__PURE__*/React.createElement("div", null, /*#__PURE__*/React.createElement("h4", {
      className: "font-semibold mb-2"
    }, "\u4ED8\u6B3E\u65B9\u5F0F"), /*#__PURE__*/React.createElement("p", null, formData.paymentMethod), formData.notes && /*#__PURE__*/React.createElement("div", {
      className: "mt-2"
    }, /*#__PURE__*/React.createElement("p", {
      className: "font-semibold"
    }, "\u5099\u8A3B\uFF1A"), /*#__PURE__*/React.createElement("p", null, formData.notes)))), /*#__PURE__*/React.createElement("div", {
      className: "flex space-x-3 mt-auto p-6 pt-4 flex-shrink-0"
    }, /*#__PURE__*/React.createElement("button", {
      onClick: function onClick() {
        return setShowOrderConfirm(false);
      },
      className: "flex-1 bg-gray-300 hover:bg-gray-400 text-gray-700 py-2 rounded transition-colors"
    }, "\u8FD4\u56DE\u4FEE\u6539"), /*#__PURE__*/React.createElement("button", {
      onClick: confirmSubmitOrder,
      className: "flex-1 bg-red-600 hover:bg-red-700 text-white py-2 rounded transition-colors"
    }, "\u78BA\u8A8D\u63D0\u4EA4")))));
  } catch (error) {
    console.error('OrderForm component error:', error);
    reportError(error);
    return null;
  }
}